import asyncio
import base64
import datetime
import hmac
import io
import json
import time
import uuid
from hashlib import sha1
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from openai import OpenAI
from PIL import Image
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from starlette_context import context

from llmbot.api.deps import get_async_db
from llmbot.core.config import settings
from llmbot.models.chatbot_session_group import ChatbotSessionGroupModel
from llmbot.utils.moderation import ModerationResult, aliyun_moderation, openai_moderation
from llmbot.utils.oss import Oss

router = APIRouter()


class GptImageRequest(BaseModel):
    prompt: str
    size: str = "auto"
    quality: str = "low"
    transparent: bool = True
    ref_image_urls: Optional[List[str]] = None


class GptImageResponse(BaseModel):
    image_url: str
    message: str


@router.get("/ping")
def ping() -> Any:
    return {
        "msg": "pong",
        "data": context.data,
    }  # {"X-Request-ID": "6f7e32a469cc4e16ac8356fc2f5a2bc7", "pid": 21}


@router.get("/songs", response_model=list[ChatbotSessionGroupModel])
async def get_songs(session: AsyncSession = Depends(get_async_db)):
    result = await session.execute(select(ChatbotSessionGroupModel))
    songs = result.scalars().all()

    return [ChatbotSessionGroupModel(name=song.name) for song in songs]


@router.get("/text/moderation")
async def text_moderation(user_input: str) -> ModerationResult:
    openai_task = asyncio.create_task(openai_moderation(user_input))
    aliyun_task = asyncio.create_task(aliyun_moderation(user_input))
    results = await asyncio.gather(openai_task, aliyun_task)
    passed = True
    for result in results:
        if not result.passed:
            passed = False
    return ModerationResult(passed=passed)


@router.get("/oss/signature")
def new_oss_signature():
    expire_time = 2 * 60
    now = int(time.time())
    expire_syncpoint = now + expire_time
    expire = datetime.datetime.fromtimestamp(expire_syncpoint, datetime.timezone.utc).strftime(
        "%Y-%m-%dT%H:%M:%SZ"
    )
    policy_dict = {}
    policy_dict["expiration"] = expire
    condition_array = []
    array_item = []
    array_item.append("starts-with")
    array_item.append("$key")
    array_item.append("")
    condition_array.append(array_item)
    policy_dict["conditions"] = condition_array
    policy = json.dumps(policy_dict).strip()
    policy_encode = base64.b64encode(policy.encode())
    h = hmac.new(settings.OSS_ACCESS_KEY.encode(), policy_encode, sha1)
    sign_result = base64.encodebytes(h.digest()).strip()
    return {
        "accessKeyId": settings.OSS_ACCESS_ID,
        "domain": f"https://{settings.OSS_BUCKET}.{settings.OSS_PUBLIC_ENDPOINT}",
        "url": f"https://{settings.OSS_BUCKET}.{settings.OSS_PUBLIC_ENDPOINT}",
        "policy": policy_encode.decode(),
        "signature": sign_result.decode(),
    }


@router.post("/gpt-image", response_model=GptImageResponse)
def gpt_image_generate(request: GptImageRequest) -> GptImageResponse:
    """Generate or edit images using OpenAI's gpt-image-1 model.

    Args:
        prompt: Text description for image generation/editing
        size: Image size (default: "auto")
        quality: Image quality (default: "low")
        transparent: Whether to use transparent background (default: True)
        ref_images: Optional reference images for editing

    Returns:
        GptImageResponse with image URL and message
    """
    try:
        # Initialize OpenAI client
        client = OpenAI(
            base_url="https://api.laozhang.ai/v1",
            api_key=settings.LAOZHANG_API_KEY,
        )

        # Process reference images if provided
        image_files = []
        if request.ref_image_urls:
            for image_url in request.ref_image_urls:
                try:
                    import requests

                    response = requests.get(image_url)
                    response.raise_for_status()
                    image_files.append(io.BytesIO(response.content))
                except Exception as e:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Failed to download reference image {image_url}: {e!s}",
                    )

        # Generate or edit image
        if image_files:
            result = client.images.edit(
                model="gpt-image-1",
                image=image_files,
                prompt=request.prompt,
                quality=request.quality,
                size=request.size,
                background="transparent" if request.transparent else None,
                n=1,
            )
        else:
            # Generation mode
            result = client.images.generate(
                model="gpt-image-1",
                prompt=request.prompt,
                quality=request.quality,
                size=request.size,
                output_format="png" if request.transparent else None,
                background="transparent" if request.transparent else None,
                n=1,
            )

        # Process the generated image
        image_base64 = result.data[0].b64_json
        image_bytes = base64.b64decode(image_base64)
        image = Image.open(io.BytesIO(image_bytes))

        if request.transparent and image.mode != "RGBA":
            image = image.convert("RGBA")

        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = "png" if request.transparent else "jpg"
        file_name = f"gpt-image/{file_id}.{file_extension}"

        # Save image to memory buffer
        img_buffer = io.BytesIO()
        if request.transparent:
            image.save(img_buffer, format="PNG")
        else:
            image.save(img_buffer, format="JPEG")
        img_buffer.seek(0)

        # Upload to OSS using utility function
        image_url = Oss.upload_data(img_buffer.getvalue(), file_name)

        return GptImageResponse(
            image_url=image_url,
            message=(
                f"Image generated successfully with "
                f"{'transparent' if request.transparent else 'solid'} background"
            ),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate image: {e!s}")
